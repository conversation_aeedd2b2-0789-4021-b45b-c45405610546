{"name": "freight-admin", "version": "2.0.1", "type": "module", "scripts": {"dev": "vite", "test": "vite --mode test", "build:prod": "vite build --mode production", "preview:stage": "pnpm build:stage && vite preview", "preview:prod": "pnpm build:prod && vite preview", "lint:eslint": "eslint --cache --max-warnings 0 \"{src,tests,types}/**/*.{vue,js,ts}\" --fix", "lint:prettier": "prettier --write \"{src,tests,types}/**/*.{vue,js,ts,json,css,less,scss,html,md}\"", "lint": "pnpm lint:eslint && pnpm lint:prettier"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "axios": "1.7.5", "dayjs": "1.11.13", "element-plus": "2.8.1", "lodash-es": "4.17.21", "mitt": "3.0.1", "normalize.css": "8.0.1", "nprogress": "0.2.0", "path-browserify": "1.0.1", "path-to-regexp": "7.1.0", "pinia": "2.2.2", "screenfull": "6.0.2", "vue": "3.4.38", "vue-json-pretty": "^2.2.4", "vue-router": "4.4.3"}, "devDependencies": {"@types/lodash-es": "4.17.12", "@types/node": "22.5.0", "@types/nprogress": "0.2.3", "@types/path-browserify": "1.0.3", "@typescript-eslint/eslint-plugin": "8.2.0", "@typescript-eslint/parser": "8.2.0", "@vitejs/plugin-vue": "5.1.2", "@vue/eslint-config-prettier": "9.0.0", "@vue/eslint-config-typescript": "13.0.0", "eslint": "8.57.0", "eslint-plugin-prettier": "5.2.1", "eslint-plugin-vue": "9.27.0", "lint-staged": "15.2.9", "prettier": "3.3.3", "sass": "1.77.8", "typescript": "5.5.4", "vite": "5.4.2", "vite-plugin-svg-icons": "2.0.1", "vite-svg-loader": "5.1.0", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.0.29"}, "lint-staged": {"*.{vue,js,ts}": ["eslint --fix", "prettier --write"], "*.{css,less,scss,html,md}": ["prettier --write"], "package.json": ["prettier --write"]}, "license": "MIT"}