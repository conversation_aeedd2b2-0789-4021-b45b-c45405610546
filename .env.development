# 请勿改动这一项，该项也不可以通过 import.meta.env.NODE_ENV 调用
# NODE_ENV = development

# 下面是自定义的环境变量，可以修改（命名必须以 VITE_ 开头）

# 开发环境自定义的环境变量（命名必须以 VITE_ 开头）

## 后端接口公共路径（如果解决跨域问题采用反向代理就只需写公共路径）
VITE_BASE_API = '/api'

## 前端端口
VITE_CLI_PORT = 8080

## 后端地址
VITE_BASE_PATH = http://192.168.3.154

## 后端端口
VITE_SERVER_PORT = 8080

## 路由模式 hash 或 html5
VITE_ROUTER_HISTORY = 'hash'

## 开发环境地址前缀（一般 '/'，'./' 都可以）
VITE_PUBLIC_PATH = '/'
