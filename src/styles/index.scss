// 全局 CSS 变量
@import "./variables.css";
// Transition
@import "./transition.scss";
// Element Plus
@import "./element-plus.css";
@import "./element-plus.scss";
// 注册多主题
@import "./theme/register.scss";
// mixin
@import "./mixins.scss";
// View Transition
@import "./view-transition.scss";
// 组件通用样式
@import "freight.scss";

// 业务页面几乎都应该在根元素上挂载 class="app-container"，以保持页面美观
.app-container {
  padding: 8px;
}

html {
  height: 100%;
}

body {
  height: 100%;
  color: var(--v3-body-text-color);
  background-color: var(--v3-body-bg-color);
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial,
    sans-serif;
    @extend %scrollbar;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

a,
a:focus,
a:hover {
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}
