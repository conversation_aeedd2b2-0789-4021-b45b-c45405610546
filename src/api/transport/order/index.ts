import { request } from "@/utils/service"

// 订单状态枚举
export enum OrderStatusEnum {
  PENDING = 1, // 待确认
  CONFIRMED = 2, // 已确认
  IN_TRANSIT = 3, // 运输中
  DELIVERED = 4, // 已送达
  CANCELLED = 5 // 已取消
}

// 订单状态显示文本
export const OrderStatusText = {
  [OrderStatusEnum.PENDING]: "待确认",
  [OrderStatusEnum.CONFIRMED]: "已确认",
  [OrderStatusEnum.IN_TRANSIT]: "运输中",
  [OrderStatusEnum.DELIVERED]: "已送达",
  [OrderStatusEnum.CANCELLED]: "已取消"
}

// 订金状态枚举
export enum DepositStatusEnum {
  UNPAID = 1, // 未支付
  PAID = 2, // 已支付
  REFUNDED = 3 // 已退款
}

// 订金状态显示文本
export const DepositStatusText = {
  [DepositStatusEnum.UNPAID]: "未支付",
  [DepositStatusEnum.PAID]: "已支付",
  [DepositStatusEnum.REFUNDED]: "已退款"
}

// 订单数据接口
interface OrderData {
  id: number
  phone: string
  loadingAddressName: string
  unloadingAddressName: string
  cargoName: string
  depositStatus: number | null
  orderStatus: number | null
  createdAt: string
}

export interface OrderDataModel extends OrderData, FreightModel {}

// 列表数据类型
export type OrderListData = ListData<OrderDataModel[]>

// 搜索参数接口
interface ReqOrderList {
  id?: string
  phone?: string
  loadingAddressName?: string
  unloadingAddressName?: string
  cargoName?: string
  depositStatus?: number
  orderStatus?: number
  page: number
  limit: number
}

// 获取订单列表
export function getOrderListApi(data: ReqOrderList) {
  return request<ApiResponseData<OrderListData>>({
    url: "/transport/order/getOrderList",
    method: "post",
    data
  })
}
