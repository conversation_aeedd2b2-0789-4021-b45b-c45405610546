import { request } from "@/utils/service"

// 订单状态枚举
export enum OrderStatusEnum {
  // 发货中
  PENDING_ORDER = 0, // 待接单

  // 运输中
  ACCEPTED = 1, // 已接单
  PRICE_CHANGE_PENDING = 2, // 改价待确认
  PRICE_CHANGE_CONFIRMED = 3, // 改价已确认
  PRICE_CHANGE_REJECTED = 4, // 改价已拒绝
  ARRIVED_LOADING = 5, // 到达装货地
  LOADING = 6, // 装货中
  IN_TRANSIT = 7, // 司机运输中
  ARRIVED_UNLOADING = 8, // 到达卸货地
  COMPLETED_PENDING = 9, // 已完成待确认

  // 已完成
  COMPLETED_CONFIRMED = 10, // 已完成已确认

  // 已取消
  CANCELLED = 98, // 已取消

  // 已完成（非正常流程）
  COMPLETED_ABNORMAL = 99 // 已完成（非正常流程）
}

// 订单状态显示文本
export const OrderStatusText = {
  [OrderStatusEnum.PENDING_ORDER]: "待接单",
  [OrderStatusEnum.ACCEPTED]: "已接单",
  [OrderStatusEnum.PRICE_CHANGE_PENDING]: "改价待确认",
  [OrderStatusEnum.PRICE_CHANGE_CONFIRMED]: "改价已确认",
  [OrderStatusEnum.PRICE_CHANGE_REJECTED]: "改价已拒绝",
  [OrderStatusEnum.ARRIVED_LOADING]: "到达装货地",
  [OrderStatusEnum.LOADING]: "装货中",
  [OrderStatusEnum.IN_TRANSIT]: "司机运输中",
  [OrderStatusEnum.ARRIVED_UNLOADING]: "到达卸货地",
  [OrderStatusEnum.COMPLETED_PENDING]: "已完成待确认",
  [OrderStatusEnum.COMPLETED_CONFIRMED]: "已完成已确认",
  [OrderStatusEnum.CANCELLED]: "已取消",
  [OrderStatusEnum.COMPLETED_ABNORMAL]: "已完成（非正常流程）"
}

// 订金状态枚举
export enum DepositStatusEnum {
  NO_DEPOSIT = 0, // 无订金
  PAID_PENDING_REFUND = 1, // 已支付（待发起退订金）
  PAID_DRIVER_INITIATED_REFUND = 2, // 已支付（司机已发起退订金）
  PAID_OWNER_AGREED_REFUND = 3, // 已支付（货主同意退订金）
  PAID_OWNER_REJECTED_REFUND = 4, // 已支付（货主拒绝退订金）
  PAID_DEPOSIT_FROZEN = 5, // 已支付（订金冻结）
  PAID_OWNER_INITIATED_DEDUCT = 6, // 已支付（货主已发起扣订金）
  PAID_DRIVER_AGREED_DEDUCT = 7, // 已支付（司机同意扣订金）
  PAID_DRIVER_REJECTED_DEDUCT = 8, // 已支付（司机拒绝扣订金）
  ARBITRATION_COMPLETED = 9, // 仲裁受理完毕
  REFUNDED_TO_DRIVER = 10, // 订金已退还司机
  PAID_TO_OWNER = 11 // 订金已支付货主
}

// 订金状态显示文本
export const DepositStatusText = {
  [DepositStatusEnum.NO_DEPOSIT]: "无订金",
  [DepositStatusEnum.PAID_PENDING_REFUND]: "已支付（待发起退订金）",
  [DepositStatusEnum.PAID_DRIVER_INITIATED_REFUND]: "已支付（司机已发起退订金）",
  [DepositStatusEnum.PAID_OWNER_AGREED_REFUND]: "已支付（货主同意退订金）",
  [DepositStatusEnum.PAID_OWNER_REJECTED_REFUND]: "已支付（货主拒绝退订金）",
  [DepositStatusEnum.PAID_DEPOSIT_FROZEN]: "已支付（订金冻结）",
  [DepositStatusEnum.PAID_OWNER_INITIATED_DEDUCT]: "已支付（货主已发起扣订金）",
  [DepositStatusEnum.PAID_DRIVER_AGREED_DEDUCT]: "已支付（司机同意扣订金）",
  [DepositStatusEnum.PAID_DRIVER_REJECTED_DEDUCT]: "已支付（司机拒绝扣订金）",
  [DepositStatusEnum.ARBITRATION_COMPLETED]: "仲裁受理完毕",
  [DepositStatusEnum.REFUNDED_TO_DRIVER]: "订金已退还司机",
  [DepositStatusEnum.PAID_TO_OWNER]: "订金已支付货主"
}

// 订单数据接口
interface OrderData {
  id: number
  mobile: string
  loadingAddress: {
    province: string
    city: string
    district: string
  }
  dischargeAddress: {
    province: string
    city: string
    district: string
  }
  cargoName: string
  weightMin: number | null
  weightMax: number | null
  volumeMin: number | null
  volumeMax: number | null
  bidAmount: number
  bidType: string
  depositRefundType: string
  depositAmount: number
  depositStatus: number | null
  orderStatus: number | null
  createTime: string
}

export interface OrderDataModel extends OrderData, FreightModel {}

// 列表数据类型
export type OrderListData = ListData<OrderDataModel[]>

// 搜索参数接口
interface ReqOrderList {
  id?: string
  mobile?: string
  loadingAddress?: string
  unloadingAddress?: string
  cargoName?: string
  depositStatus?: number
  orderStatus?: number
  startTime?: string
  endTime?: string
  page: number
  limit: number
}

// 获取订单列表
export function getOrderListApi(data: ReqOrderList) {
  return request<ApiResponseData<OrderListData>>({
    url: "/transport/orderInfo/orderInfoPage",
    method: "post",
    data
  })
}
