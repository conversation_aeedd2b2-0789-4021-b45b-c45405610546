import { request } from "@/utils/service"

// 用户身份枚举
export enum UserIdentityEnum {
  OWNER_PERSONAL = 1, // 货主（个人）
  DRIVER = 2, // 司机
  STATION = 3, // 配货站
  OWNER_ENTERPRISE = 4 // 货主（企业）
}

// 用户身份显示文本
export const UserIdentityText = {
  [UserIdentityEnum.OWNER_PERSONAL]: "货主（个人）",
  [UserIdentityEnum.DRIVER]: "司机",
  [UserIdentityEnum.STATION]: "配货站",
  [UserIdentityEnum.OWNER_ENTERPRISE]: "货主（企业）"
}

// 审核状态枚举
export enum AuditStatusEnum {
  NOT_AUDITED = 0, // 未审核
  PENDING = 1, // 待审核
  APPROVED = 2, // 已审核
  REJECTED = 3 // 审核不通过
}

// 审核状态显示文本
export const AuditStatusText = {
  [AuditStatusEnum.NOT_AUDITED]: "未审核",
  [AuditStatusEnum.PENDING]: "待审核",
  [AuditStatusEnum.APPROVED]: "已审核",
  [AuditStatusEnum.REJECTED]: "审核不通过"
}

// 资质审核数据接口
interface QualificationData {
  userId: number
  mobile: string
  fullName: string
  idCardNumber: string
  userType: number | null
  auditStatus: number | null
  createdAt: string
}

export interface QualificationDataModel extends QualificationData, FreightModel {}

// 列表数据类型
export type QualificationListData = ListData<QualificationDataModel[]>

// 搜索参数接口
interface ReqQualificationList {
  mobile?: string
  fullName?: string
  idCardNumber?: string
  userType?: number
  auditStatus?: number
  auditApplyBeginTime?: string
  auditApplyEndTime?: string
  failReason?: string
  page: number
  limit: number
  userIds?: string
}

// 获取资质审核列表
export function getQualificationListApi(data: ReqQualificationList) {
  return request<ApiResponseData<QualificationListData>>({
    url: "/transport/user/getAuditUserPage",
    method: "post",
    data
  })
}

// 审核用户接口参数
interface AuditUserParams {
  userIds?: string
  auditStatus: number // 审核状态：2-通过，3-驳回
  failReason?: string // 驳回原因（当auditStatus为3时必填）
}

// 统一审核接口
export function auditUserApi(data: AuditUserParams) {
  return request<ApiResponseData<null>>({
    url: "/transport/user/auditUser",
    method: "post",
    data
  })
}
