import { request } from "@/utils/service"

// 车长列表
export function getCarLengthList(params: any) {
  return request({
    url: "/carLength/carLengthList",
    method: "get",
    params
  })
}

// 新增车长
export function addCarLength(data: any) {
  return request({
    url: "/carLength/addCarLength",
    method: "post",
    data
  })
}

// 编辑车长
export function editCarLength(data: any) {
  return request({
    url: "/carLength/editCarLength",
    method: "put",
    data
  })
}

// 删除车长
export function deleteCarLength(data: any) {
  return request({
    url: "/carLength/deleteCarLength",
    method: "delete",
    data
  })
}
