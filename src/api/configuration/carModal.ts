import { request } from "@/utils/service"

// 车型列表
export function getModel() {
  return request({
    url: "/model/getModel",
    method: "get"
  })
}

// 新增车型
export function addModel(data: any) {
  return request({
    url: "/model/addModel",
    method: "post",
    data
  })
}

// 删除车型
export function delModel(data: any) {
  return request({
    url: "/model/delModel",
    method: "delete",
    data
  })
}

// 编辑车型
export function editModel(data: any) {
  return request({
    url: "/model/editModel",
    method: "put",
    data
  })
}

// 编辑车型启动状态
export function switchEnableApi(data: any) {
  return request({
    url: "/model/switchEnable",
    method: "put",
    data
  })
}

export function getAll() {
  return request({
    url: "/packing/getTrunkType",
    method: "get"
  })
}
