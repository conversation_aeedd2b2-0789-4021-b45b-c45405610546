import { request } from "@/utils/service"

// 包装方式列表
export function getPacking(params: any) {
  return request({
    url: "/packing/getPacking",
    method: "get",
    params
  })
}

// 新增包装方式
export function addPacking(data: any) {
  return request({
    url: "/packing/addPacking",
    method: "post",
    data
  })
}

// 编辑包装方式
export function editPacking(data: any) {
  return request({
    url: "/packing/editPacking",
    method: "put",
    data
  })
}

// 编辑包装方式启动状态
export function switchEnableApi(data: any) {
  return request({
    url: "/packing/switchEnable",
    method: "put",
    data
  })
}

// 删除包装方式
export function delPacking(data: any) {
  return request({
    url: "/packing/delPacking",
    method: "delete",
    data
  })
}

// 批量删除包装方式
export function batchDelPacking(data: any) {
  return request({
    url: "/packing/batchDelPacking",
    method: "delete",
    data
  })
}
