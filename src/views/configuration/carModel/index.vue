<template>
  <div class="app-container">
    <el-card v-loading="loading" shadow="never">
      <div class="toolbar-wrapper">
        <div>
          <el-button type="primary" icon="CirclePlus" @click="addDialog">新增</el-button>
        </div>
        <div>
          <el-tooltip content="刷新" effect="light">
            <el-button type="primary" icon="RefreshRight" circle plain @click="getTableData" />
          </el-tooltip>
        </div>
      </div>
      <div class="table-wrapper">
        <el-table :data="tableData" @selection-change="handleSelectionChange">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="车型" />
          <el-table-column prop="enable" label="状态">
            <template #default="scope">
              <el-switch
                v-model="scope.row.enable"
                inline-prompt
                :active-value="true"
                :inactive-value="false"
                active-text="启用"
                inactive-text="禁用"
                @change="switchEnable(scope.row.id, scope.row.enable)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="createdAt" label="创建时间">
            <template #default="scope">
              {{ formatDateTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="updatedAt" label="更新时间">
            <template #default="scope">
              {{ formatDateTime(scope.row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" text icon="Edit" size="small" @click="editDialog(scope.row)">编辑</el-button>
              <el-button type="danger" text icon="Delete" size="small" @click="handleDeleteApi(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :before-close="handleClose" width="38%">
      <warning-bar title="车型，用于APP端发货配置" />
      <el-form ref="formRef" :model="opFormData" :rules="addFormRules" label-width="80px">
        <el-form-item label="车型名称" prop="name">
          <el-input v-model="opFormData.name" />
        </el-form-item>
        <el-form-item label="车型描述" prop="description">
          <el-input v-model="opFormData.description" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="operateAction(formRef)">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue"
import { type FormInstance, type FormRules, ElMessage, ElMessageBox, ElNotification } from "element-plus"
import { getModel, addModel, delModel, editModel, switchEnableApi } from "@/api/configuration/carModal"
import WarningBar from "@/components/WarningBar/warningBar.vue"
import { formatDateTime } from "@/utils/index"

defineOptions({
  name: "CarModel"
})

const loading = ref(false)

const tableData = ref([])

const getTableData = async () => {
  loading.value = true
  try {
    const result: any = await getModel()
    if (result.code == 0) {
      tableData.value = result.data
    }
  } catch (error) {
    //
  }
  loading.value = false
}
getTableData()

const ids = ref<number[]>([])
const taskIds = ref("") // for csv
const handleSelectionChange = (val: any) => {
  ids.value = val.map((item: any) => item.id)
  taskIds.value = ids.value.join(",")
}

// 对话框
const formRef = ref<FormInstance>()
const opFormData = reactive({
  name: "",
  description: ""
})

enum operationKind {
  Add = "Add",
  Edit = "Edit"
}

let oKind: operationKind
const addFormRules: FormRules = reactive({
  name: [{ required: true, trigger: "blur", message: "包装方式不能为空" }],
  description: [{ required: true, trigger: "blur", message: "描述不能为空" }]
})

const initForm = () => {
  formRef.value?.resetFields()
  opFormData.name = ""
  opFormData.description = ""
}

const dialogVisible = ref(false)
const dialogTitle = ref("")
const handleClose = (done: Function) => {
  initForm()
  done()
}

const addDialog = () => {
  dialogTitle.value = "新增包装方式"
  oKind = operationKind.Add
  dialogVisible.value = true
}

const closeDialog = () => {
  dialogVisible.value = false
  initForm()
}

const operateAction = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      if (oKind === "Add") {
        const res: any = await addModel({ ...opFormData })
        if (res.code === 0) {
          ElMessage({ type: "success", message: res.msg })
          getTableData()
        }
      } else if (oKind === "Edit") {
        const res: any = await editModel({ id: activeRow.id, ...opFormData })
        if (res.code === 0) {
          ElMessage({ type: "success", message: res.msg })
          getTableData()
        }
      }
      closeDialog()
    }
  })
}

// 删除api
const handleDeleteApi = (row: any) => {
  ElMessageBox.confirm("此操作将删除该车型, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      delModel({ id: row.id }).then((res: any) => {
        if (res.code === 0) {
          ElMessage({ type: "success", message: res.msg })
          getTableData()
        }
      })
    })
    .catch(() => {})
}

// 编辑dialog
let activeRow: any = {}
const editDialog = (row: any) => {
  dialogTitle.value = "编辑包装方式"
  oKind = operationKind.Edit
  opFormData.description = row.description
  opFormData.name = row.name
  activeRow = row
  dialogVisible.value = true
}

// 切换启用状态
const switchEnable = (id: number, enable: boolean) => {
  switchEnableApi({ id: id, enable: enable })
    .then((res: any) => {
      if (res.code === 0) {
        if (enable) {
          ElMessage({ type: "success", message: "启用成功" })
        } else {
          ElMessage({ type: "success", message: "禁用成功" })
        }
      }
    })
    .catch(() => {})
}
</script>

<style lang="scss" scoped></style>
