<template>
  <div class="app-container">
    <el-card v-loading="loading" shadow="never" class="search-wrapper">
      <el-form ref="searchFormRef" :inline="true" :model="searchFormData">
        <el-form-item prop="typeName" label="包装方式">
          <el-input v-model="searchFormData.typeName" placeholder="包装方式" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="loading" shadow="never">
      <div class="toolbar-wrapper">
        <div>
          <el-button type="primary" icon="CirclePlus" @click="addDialog">新增</el-button>
          <el-popover v-model:visible="deleteVisible" placement="top" width="160">
            <p>确定要删除吗？</p>
            <div style="text-align: right; margin-top: 8px">
              <el-button text @click="deleteVisible = false">取消</el-button>
              <el-button type="primary" @click="onDelete">确定</el-button>
            </div>
            <template #reference>
              <el-button
                icon="delete"
                type="danger"
                plain
                :disabled="!ids.length"
                style="margin-left: 10px"
                @click="deleteVisible = true"
                >删除</el-button
              >
            </template>
          </el-popover>
        </div>
        <div>
          <el-tooltip content="刷新" effect="light">
            <el-button type="primary" icon="RefreshRight" circle plain @click="getTableData" />
          </el-tooltip>
        </div>
      </div>
      <div class="table-wrapper">
        <el-table :data="tableData" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="60" />
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="typeName" label="包装方式" />
          <el-table-column prop="enable" label="状态">
            <template #default="scope">
              <el-switch
                v-model="scope.row.enable"
                inline-prompt
                :active-value="true"
                :inactive-value="false"
                active-text="启用"
                inactive-text="禁用"
                @change="switchEnable(scope.row.id, scope.row.enable)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" />
          <el-table-column prop="createdAt" label="创建时间">
            <template #default="scope">
              {{ formatDateTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column prop="updatedAt" label="更新时间">
            <template #default="scope">
              {{ formatDateTime(scope.row.updatedAt) }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" text icon="Edit" size="small" @click="editDialog(scope.row)">编辑</el-button>
              <el-button type="danger" text icon="Delete" size="small" @click="handleDeleteApi(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pager-wrapper">
        <el-pagination
          background
          :layout="paginationData.layout"
          :page-sizes="paginationData.pageSizes"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
          :currentPage="paginationData.currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :before-close="handleClose" width="38%">
      <warning-bar title="包装方式，用于APP端发货配置" />
      <el-form ref="formRef" :model="opFormData" :rules="addFormRules" label-width="80px">
        <el-form-item label="包装方式" prop="typeName">
          <el-input v-model="opFormData.typeName" />
        </el-form-item>
        <el-form-item label="包装描述" prop="description">
          <el-input v-model="opFormData.description" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="operateAction(formRef)">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue"
import { type FormInstance, type FormRules, ElMessage, ElMessageBox, ElNotification } from "element-plus"
import { usePagination } from "@/hooks/usePagination"
import {
  getPacking,
  addPacking,
  editPacking,
  switchEnableApi,
  delPacking,
  batchDelPacking
} from "@/api/configuration/packing"
import WarningBar from "@/components/WarningBar/warningBar.vue"
import { formatDateTime } from "@/utils/index"

defineOptions({
  name: "Packing"
})

const { paginationData, changeCurrentPage, changePageSize } = usePagination()

const loading = ref(false)
const searchFormData = reactive({
  typeName: ""
})

const handleSearch = () => {
  paginationData.currentPage = 1
  paginationData.pageSize = 10
  getTableData()
}

const resetSearch = () => {
  searchFormData.typeName = ""
}

const tableData = ref([])

const getTableData = async () => {
  loading.value = true
  try {
    const res: any = await getPacking({
      typeName: searchFormData.typeName,
      page: paginationData.currentPage,
      pageSize: paginationData.pageSize
    })
    if (res.code === 0) {
      tableData.value = res.data.list
      paginationData.total = res.data.total
    }
  } catch (error) {
    //
  }
  loading.value = false
}
getTableData()

// 分页
const handleSizeChange = (value: number) => {
  changePageSize(value)
  getTableData()
}

const handleCurrentChange = (value: number) => {
  changeCurrentPage(value)
  getTableData()
}

const ids = ref<number[]>([])
const taskIds = ref("") // for csv
const handleSelectionChange = (val: any) => {
  ids.value = val.map((item: any) => item.id)
  taskIds.value = ids.value.join(",")
}

// 对话框
const formRef = ref<FormInstance>()
const opFormData = reactive({
  typeName: "",
  description: ""
})

enum operationKind {
  Add = "Add",
  Edit = "Edit"
}

let oKind: operationKind
const addFormRules: FormRules = reactive({
  typeName: [{ required: true, trigger: "blur", message: "包装方式不能为空" }],
  description: [{ required: true, trigger: "blur", message: "描述不能为空" }]
})

const initForm = () => {
  formRef.value?.resetFields()
  opFormData.typeName = ""
  opFormData.description = ""
}

const dialogVisible = ref(false)
const dialogTitle = ref("")
const handleClose = (done: Function) => {
  initForm()
  done()
}

const addDialog = () => {
  dialogTitle.value = "新增包装方式"
  oKind = operationKind.Add
  dialogVisible.value = true
}

const closeDialog = () => {
  dialogVisible.value = false
  initForm()
}

const operateAction = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      if (oKind === "Add") {
        const res: any = await addPacking({ ...opFormData })
        if (res.code === 0) {
          ElMessage({ type: "success", message: res.msg })
          getTableData()
        }
      } else if (oKind === "Edit") {
        const res: any = await editPacking({ id: activeRow.id, ...opFormData })
        if (res.code === 0) {
          ElMessage({ type: "success", message: res.msg })
          getTableData()
        }
      }
      closeDialog()
    }
  })
}

// 删除api
const handleDeleteApi = (row: any) => {
  ElMessageBox.confirm("此操作将删除该包装方式, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      delPacking({ id: row.id }).then((res: any) => {
        if (res.code === 0) {
          ElMessage({ type: "success", message: res.msg })
          getTableData()
        }
      })
    })
    .catch(() => {})
}

// 编辑dialog
let activeRow: any = {}
const editDialog = (row: any) => {
  dialogTitle.value = "编辑包装方式"
  oKind = operationKind.Edit
  opFormData.description = row.description
  opFormData.typeName = row.typeName
  activeRow = row
  dialogVisible.value = true
}

const deleteVisible = ref(false)
const onDelete = async () => {
  if (ids.value.length === 0) {
    ElNotification({
      title: "警告",
      message: "请选择记录",
      type: "warning"
    })
    return
  }
  const res: any = await batchDelPacking({ ids: ids.value })
  if (res.code === 0) {
    ElMessage({
      type: "success",
      message: res.msg
    })
    deleteVisible.value = false
    getTableData()
  }
}

// 切换启用状态
const switchEnable = (id: number, enable: boolean) => {
  switchEnableApi({ id: id, enable: enable })
    .then((res: any) => {
      if (res.code === 0) {
        if (enable) {
          ElMessage({ type: "success", message: "启用成功" })
        } else {
          ElMessage({ type: "success", message: "禁用成功" })
        }
      }
    })
    .catch(() => {})
}
</script>

<style lang="scss" scoped>
.search-wrapper {
  margin-bottom: 5px;
  :deep(.el-card__body) {
    padding-bottom: 2px;
  }
}
</style>
