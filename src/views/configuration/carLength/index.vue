<template>
  <div class="app-container">
    <el-card v-loading="loading" shadow="never" class="search-wrapper">
      <el-form ref="searchFormRef" :inline="true" :model="searchFormData">
        <el-form-item prop="maxLength" label="最大长度">
          <el-input v-model="searchFormData.maxLength" placeholder="最大长度" />
        </el-form-item>
        <el-form-item prop="maxWidth" label="最大宽度">
          <el-input v-model="searchFormData.maxWidth" placeholder="最大宽度" />
        </el-form-item>
        <el-form-item prop="maxHeight" label="最大高度">
          <el-input v-model="searchFormData.maxHeight" placeholder="最大高度" />
        </el-form-item>
        <el-form-item prop="maxLoad" label="最大载重">
          <el-input v-model="searchFormData.maxLoad" placeholder="最大载重" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="getTableData">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card v-loading="loading" shadow="never">
      <div class="toolbar-wrapper">
        <div>
          <el-button type="primary" icon="CirclePlus" @click="addDialog">新增</el-button>
        </div>
        <div>
          <el-tooltip content="刷新" effect="light">
            <el-button type="primary" icon="RefreshRight" circle plain @click="getTableData" />
          </el-tooltip>
        </div>
      </div>
      <div class="table-wrapper">
        <el-table :data="tableData">
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="minLength" label="最小长度（米）" />
          <el-table-column prop="maxLength" label="最大长度（米）" />
          <el-table-column prop="minWidth" label="最小宽度（米）" />
          <el-table-column prop="maxWidth" label="最大宽度（米）" />
          <el-table-column prop="minHeight" label="最小高度（米）" />
          <el-table-column prop="maxHeight" label="最大高度（米）" />
          <el-table-column prop="minLoad" label="最小载重（吨）" />
          <el-table-column prop="maxLoad" label="最大载重（吨）" />
          <el-table-column prop="minVolume" label="最小体积（方）" />
          <el-table-column prop="maxVolume" label="最大体积（方）" />
          <el-table-column prop="names" label="车型" :showOverflowTooltip="true" />
          <el-table-column label="操作" width="180">
            <template #default="scope">
              <el-button type="primary" text icon="Edit" size="small" @click="editDialog(scope.row)">编辑</el-button>
              <el-button type="danger" text icon="Delete" size="small" @click="handleDeleteApi(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :before-close="handleClose" width="500">
      <warning-bar title="车长参数，用于APP端发货配置" />
      <el-form ref="formRef" :model="opFormData" :rules="rules" label-width="80px">
        <el-form-item label="最小长度" prop="minLength">
          <el-input type="number" v-model="opFormData.minLength" placeholder="最小长度">
            <template #suffix> 米 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最大长度" prop="maxLength">
          <el-input type="number" v-model="opFormData.maxLength" placeholder="最大长度">
            <template #suffix> 米 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最小宽度" prop="minWidth">
          <el-input type="number" v-model="opFormData.minWidth" placeholder="最小宽度">
            <template #suffix> 米 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最大宽度" prop="maxWidth">
          <el-input type="number" v-model="opFormData.maxWidth" placeholder="最大宽度">
            <template #suffix> 米 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最小高度" prop="minHeight">
          <el-input type="number" v-model="opFormData.minHeight" placeholder="最小高度">
            <template #suffix> 米 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最大高度" prop="maxHeight">
          <el-input type="number" v-model="opFormData.maxHeight" placeholder="最大高度">
            <template #suffix> 米 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最小载重" prop="minLoad">
          <el-input type="number" v-model="opFormData.minLoad" placeholder="最小载重">
            <template #suffix> 吨 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最大载重" prop="maxLoad">
          <el-input type="number" v-model="opFormData.maxLoad" placeholder="最大载重">
            <template #suffix> 吨 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最小体积" prop="minVolume">
          <el-input type="number" v-model="opFormData.minVolume" placeholder="最小体积">
            <template #suffix> 方 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="最大体积" prop="maxVolume">
          <el-input type="number" v-model="opFormData.maxVolume" placeholder="最大体积">
            <template #suffix> 方 </template>
          </el-input>
        </el-form-item>
        <el-form-item label="车型" prop="carTypes">
          <el-select v-model="opFormData.carTypes" :multiple="true">
            <el-option v-for="(item, index) in carTypeList" :key="item.id" :value="item.id" :label="item.name">
              {{ item.name }}
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="operateAction(formRef)">确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue"
import { type FormInstance, type FormRules, ElMessage, ElMessageBox, ElNotification } from "element-plus"
import WarningBar from "@/components/WarningBar/warningBar.vue"
import { getModel } from "@/api/configuration/carModal"
import { getCarLengthList, addCarLength, editCarLength, deleteCarLength } from "@/api/configuration/carLength"

const loading = ref(false)

enum operationKind {
  Add = "Add",
  Edit = "Edit"
}
let oKind: operationKind
const searchFormRef = ref()
const formRef = ref()
const dialogVisible = ref(false)
const dialogTitle = ref("新增车长参数")
let opFormData: any = reactive({})
const searchFormData: any = reactive({})
const resetSearch = () => {
  searchFormRef.value.resetFields()
}
const addDialog = () => {
  dialogTitle.value = "新增车长参数"
  oKind = operationKind.Add
  dialogVisible.value = true
}
const tableData = ref([])
const getTableData = async () => {
  const result: any = await getCarLengthList({ ...searchFormData })
  if (result.code == 0) {
    tableData.value = result.data
  }
}
const editDialog = (row: any) => {
  opFormData.id = row.id
  opFormData.minLength = row.minLength
  opFormData.maxLength = row.maxLength
  opFormData.minWidth = row.minWidth
  opFormData.maxWidth = row.maxWidth
  opFormData.minHeight = row.minHeight
  opFormData.maxHeight = row.maxHeight
  opFormData.minLoad = row.minLoad
  opFormData.maxLoad = row.maxLoad
  opFormData.minVolume = row.minVolume
  opFormData.maxVolume = row.maxVolume
  const arr = []
  for (let item of row.carTypes) {
    arr.push(item.id)
  }
  opFormData.carTypes = arr
  dialogTitle.value = "编辑车长参数"
  oKind = operationKind.Edit
  dialogVisible.value = true
}
const handleDeleteApi = (row: any) => {
  ElMessageBox.confirm("此操作将删除该车长, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      deleteCarLength({ id: row.id }).then((res: any) => {
        if (res.code === 0) {
          ElMessage({ type: "success", message: res.msg })
          getTableData()
        }
      })
    })
    .catch(() => {})
}
const validatorCheck = (rule: any, value: any, callback: any) => {
  const regex = /^\d+(\.\d{1,2})?$/
  if (value === "") {
    callback(new Error(`${rule.label}不能为空`))
  } else if (!regex.test(value)) {
    callback(new Error("请输入最多保留两位小数的正数"))
  } else {
    callback()
  }
}
const rules = reactive({
  minLength: [
    {
      required: true,
      trigger: "change",
      label: "最小长度",
      validator: validatorCheck
    }
  ],
  maxLength: [
    {
      required: true,
      trigger: "change",
      label: "最大长度",
      validator: validatorCheck
    }
  ],
  minWidth: [
    {
      required: true,
      trigger: "change",
      label: "最小宽度",
      validator: validatorCheck
    }
  ],
  maxWidth: [
    {
      required: true,
      trigger: "change",
      label: "最大宽度",
      validator: validatorCheck
    }
  ],
  minHeight: [
    {
      required: true,
      trigger: "change",
      label: "最小高度",
      validator: validatorCheck
    }
  ],
  maxHeight: [
    {
      required: true,
      trigger: "change",
      label: "最大高度",
      validator: validatorCheck
    }
  ],
  minLoad: [
    {
      required: true,
      trigger: "change",
      label: "最小载重",
      validator: validatorCheck
    }
  ],
  maxLoad: [
    {
      required: true,
      trigger: "change",
      label: "最大载重",
      validator: validatorCheck
    }
  ],
  minVolume: [
    {
      required: true,
      trigger: "change",
      label: "最小体积",
      validator: validatorCheck
    }
  ],
  maxVolume: [
    {
      required: true,
      trigger: "change",
      label: "最大体积",
      validator: validatorCheck
    }
  ],
  carTypes: [
    {
      required: true,
      trigger: "change",
      message: "请选择车型"
    }
  ]
})
const initForm = () => {
  formRef.value?.resetFields()
}
const handleClose = (done: Function) => {
  initForm()
  done()
}
const closeDialog = () => {
  dialogVisible.value = false
  initForm()
}
const operateAction = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate(async (valid) => {
    if (valid) {
      const paramData: any = {
        minLength: +opFormData.minLength,
        maxLength: +opFormData.maxLength,
        minWidth: +opFormData.minWidth,
        maxWidth: +opFormData.maxWidth,
        minHeight: +opFormData.minHeight,
        maxHeight: +opFormData.maxHeight,
        minLoad: +opFormData.minLoad,
        maxLoad: +opFormData.maxLoad,
        minVolume: +opFormData.minVolume,
        maxVolume: +opFormData.maxVolume,
        carTypes: opFormData.carTypes
      }
      if (oKind === "Add") {
        const result: any = await addCarLength(paramData)
        if (result.code == 0) {
          ElMessage({ type: "success", message: "新增成功" })
          getTableData()
          closeDialog()
        }
      } else if (oKind === "Edit") {
        paramData.id = opFormData.id
        const result: any = await editCarLength(paramData)
        if (result.code == 0) {
          ElMessage({ type: "success", message: "编辑成功" })
          getTableData()
          closeDialog()
        }
      }
    }
  })
}
const carTypeList: any = ref([])
const getCarTypeList = async () => {
  const result: any = await getModel()
  if (result.code == 0) {
    carTypeList.value = result.data
  }
}
onMounted(() => {
  getTableData()
  getCarTypeList()
})
</script>

<style scoped lang="scss">
.search-wrapper {
  margin-bottom: 5px;
  :deep(.el-card__body) {
    padding-bottom: 2px;
  }
}
</style>
