<template>
  <el-dialog v-model="visible" title="认证详情（司机）" width="800px" :before-close="handleClose">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 身份证信息 -->
      <el-tab-pane label="身份证" name="idCard">
        <div class="detail-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">{{ driverData?.fullName || "-" }}</el-descriptions-item>
            <el-descriptions-item label="身份证号码">{{ driverData?.idCardNumber || "-" }}</el-descriptions-item>
          </el-descriptions>

          <div class="image-section">
            <div class="image-item">
              <div class="image-label">身份证正面照</div>
              <el-image
                v-if="driverData?.idCardPath"
                :src="driverData.idCardPath"
                :preview-src-list="[driverData.idCardPath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">身份证反面照</div>
              <el-image
                v-if="driverData?.idCardBackPath"
                :src="driverData.idCardBackPath"
                :preview-src-list="[driverData.idCardBackPath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 驾驶证信息 -->
      <el-tab-pane label="驾驶证" name="drivingLicense">
        <div class="detail-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">{{ driverData?.fullName || "-" }}</el-descriptions-item>
            <el-descriptions-item label="身份证号码">{{ driverData?.idCardNumber || "-" }}</el-descriptions-item>
          </el-descriptions>

          <div class="image-section">
            <div class="image-item">
              <div class="image-label">驾驶证主页</div>
              <el-image
                v-if="driverData?.driverLicensePath"
                :src="driverData.driverLicensePath"
                :preview-src-list="[driverData.driverLicensePath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">驾驶证副页</div>
              <el-image
                v-if="driverData?.driverLicenseBackPath"
                :src="driverData.driverLicenseBackPath"
                :preview-src-list="[driverData.driverLicenseBackPath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 车辆信息 -->
      <el-tab-pane label="车辆信息" name="vehicle">
        <div class="detail-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="车牌号码">{{ driverData?.licensePlateNumber || "-" }}</el-descriptions-item>
            <el-descriptions-item label="车辆所有人">{{ driverData?.vehicleOwner || "-" }}</el-descriptions-item>
            <el-descriptions-item label="车辆注册日期">{{
              driverData?.vehicleRegistrationDate || "-"
            }}</el-descriptions-item>
          </el-descriptions>

          <div class="image-section">
            <div class="image-item">
              <div class="image-label">行驶证主页</div>
              <el-image
                v-if="driverData?.vehicleLicensePath"
                :src="driverData.vehicleLicensePath"
                :preview-src-list="[driverData.vehicleLicensePath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">行驶证副页</div>
              <el-image
                v-if="driverData?.vehicleLicenseBackPath"
                :src="driverData.vehicleLicenseBackPath"
                :preview-src-list="[driverData.vehicleLicenseBackPath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 道路运输许可证 -->
      <el-tab-pane label="道路运输许可证" name="transportPermit">
        <div class="detail-section">
          <div class="image-section single-image">
            <div class="image-item">
              <div class="image-label">道路运输许可证</div>
              <el-image
                v-if="driverData?.transportLicensePath"
                :src="driverData.transportLicensePath"
                :preview-src-list="[driverData.transportLicensePath]"
                fit="cover"
                class="cert-image large"
              />
              <div v-else class="empty-image large">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 环保信息 -->
      <el-tab-pane label="环保信息" name="environmental">
        <div class="detail-section">
          <div class="image-section single-image">
            <div class="image-item">
              <div class="image-label">环保信息照片</div>
              <el-image
                v-if="driverData?.environmentalListPath"
                :src="driverData.environmentalListPath"
                :preview-src-list="[driverData.environmentalListPath]"
                fit="cover"
                class="cert-image large"
              />
              <div v-else class="empty-image large">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue"

// 司机详情数据接口
interface DriverDetailData {
  id: number
  fullName: string
  idCardNumber: string
  mobile: string
  // 身份证相关
  idCardFront?: string
  idCardBack?: string
  // 驾驶证相关
  drivingLicenseMain?: string
  drivingLicenseSub?: string
  // 车辆相关
  plateNumber?: string
  vehicleOwner?: string
  region?: string
  registrationDate?: string
  vehicleLicenseMain?: string
  vehicleLicenseSub?: string
  // 许可证相关
  transportPermit?: string
  // 环保信息
  environmentalInfo?: string
}

interface Props {
  modelValue: boolean
  driverData: DriverDetailData | null
}

interface Emits {
  (e: "update:modelValue", value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const activeTab = ref("idCard")

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
})

const handleClose = () => {
  visible.value = false
  activeTab.value = "idCard"
}
</script>

<style lang="scss" scoped>
.detail-section {
  padding: 16px 0;
}

.image-section {
  margin-top: 20px;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;

  &.single-image {
    justify-content: center;
  }
}

.image-item {
  flex: 1;
  min-width: 200px;
  text-align: center;

  .image-label {
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
  }
}

.cert-image {
  width: 200px;
  height: 120px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;

  &.large {
    width: 300px;
    height: 180px;
  }
}

.empty-image {
  width: 200px;
  height: 120px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  background-color: #fafafa;
  margin: 0 auto;

  &.large {
    width: 300px;
    height: 180px;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    width: 120px;
  }
}
</style>
