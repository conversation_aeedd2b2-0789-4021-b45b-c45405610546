<template>
  <el-dialog v-model="visible" title="认证详情（货主）" width="800px" :before-close="handleClose">
    <el-tabs v-model="activeTab" type="border-card">
      <!-- 身份证 -->
      <el-tab-pane label="身份证" name="idCard">
        <div class="detail-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="姓名">{{ ownerData?.fullName || "-" }}</el-descriptions-item>
            <el-descriptions-item label="身份证号码">{{ ownerData?.idCardNumber || "-" }}</el-descriptions-item>
          </el-descriptions>

          <div class="image-section">
            <div class="image-item">
              <div class="image-label">身份证正面照</div>
              <el-image
                v-if="ownerData?.idCardPath"
                :src="ownerData.idCardPath"
                :preview-src-list="[ownerData.idCardPath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
            <div class="image-item">
              <div class="image-label">身份证反面照</div>
              <el-image
                v-if="ownerData?.idCardBackPath"
                :src="ownerData.idCardBackPath"
                :preview-src-list="[ownerData.idCardBackPath]"
                fit="cover"
                class="cert-image"
              />
              <div v-else class="empty-image">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 营业执照 -->
      <el-tab-pane label="营业执照" name="businessLicense">
        <div class="detail-section">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="法人">{{ ownerData?.legalPerson || "-" }}</el-descriptions-item>
            <el-descriptions-item label="公司名称">{{ ownerData?.companyName || "-" }}</el-descriptions-item>
            <el-descriptions-item label="营业执照编号">{{
              ownerData?.businessLicenseNumber || "-"
            }}</el-descriptions-item>
            <el-descriptions-item label="税号">{{ ownerData?.taxNumber || "-" }}</el-descriptions-item>
          </el-descriptions>

          <div class="image-section single-image">
            <div class="image-item">
              <div class="image-label">营业执照</div>
              <el-image
                v-if="ownerData?.businessLicensePath"
                :src="ownerData.businessLicensePath"
                :preview-src-list="[ownerData.businessLicensePath]"
                fit="cover"
                class="cert-image large"
              />
              <div v-else class="empty-image large">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 授权书 -->
      <el-tab-pane label="授权书" name="authorizationLetter">
        <div class="detail-section">
          <!-- 验证提示 -->
          <div v-if="!isNameMatchLegalPerson" class="authorization-notice">
            <el-alert title="如遇身份证名与营业执照法人不一致，需上传授权书" type="error" :closable="false" show-icon />
          </div>

          <div class="image-section single-image">
            <div class="image-item">
              <div class="image-label">授权书</div>
              <el-image
                v-if="ownerData?.authorizationPath"
                :src="ownerData.authorizationPath"
                :preview-src-list="[ownerData.authorizationPath]"
                fit="cover"
                class="cert-image large"
              />
              <div v-else class="empty-image large">暂无图片</div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from "vue"

// 货主详情数据接口
interface OwnerDetailData {
  id: number
  fullName: string
  mobile: string
  idCardNumber: string
  // 身份证相关
  idCardFront?: string
  idCardBack?: string
  // 营业执照相关
  legalPerson?: string
  companyName?: string
  businessLicenseNumber?: string
  taxNumber?: string
  businessLicenseImage?: string
  // 授权书相关
  authorizationLetter?: string
}

interface Props {
  modelValue: boolean
  ownerData: OwnerDetailData | null
}

interface Emits {
  (e: "update:modelValue", value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const activeTab = ref("idCard")

const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value)
})

// 验证身份证姓名与营业执照法人是否一致
const isNameMatchLegalPerson = computed(() => {
  const name = props.ownerData?.fullName?.trim()
  const legalPerson = props.ownerData?.legalPerson?.trim()
  return name && legalPerson && name === legalPerson
})

const handleClose = () => {
  visible.value = false
  activeTab.value = "idCard"
}
</script>

<style lang="scss" scoped>
.detail-section {
  padding: 16px 0;
}

.image-section {
  margin-top: 20px;
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;
}

.image-item {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
  text-align: center;

  .image-label {
    margin-bottom: 8px;
    font-weight: 500;
    color: #606266;
  }
}

.cert-image {
  width: 200px;
  height: 120px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  cursor: pointer;

  &.large {
    width: 300px;
    height: 180px;
  }
}

.empty-image {
  width: 200px;
  height: 120px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c0c4cc;
  background-color: #fafafa;
  margin: 0 auto;

  &.large {
    width: 300px;
    height: 180px;
  }
}

:deep(.el-descriptions) {
  .el-descriptions__label {
    width: 140px;
  }
}

.authorization-notice {
  margin-bottom: 20px;
}
</style>
