<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card v-loading="loading" shadow="never" class="search-wrapper">
      <el-form ref="searchFormRef" :inline="true" :model="searchFormData">
        <el-form-item prop="mobile" label="手机号码">
          <el-input v-model="searchFormData.mobile" placeholder="手机号码" clearable />
        </el-form-item>
        <el-form-item prop="fullName" label="姓名">
          <el-input v-model="searchFormData.fullName" placeholder="请输入姓名" clearable />
        </el-form-item>
        <el-form-item prop="idCardNumber" label="身份证号">
          <el-input v-model="searchFormData.idCardNumber" placeholder="请输入身份证号" clearable />
        </el-form-item>
        <el-form-item prop="userType" label="用户身份">
          <el-select v-model="searchFormData.userType" placeholder="请选择用户身份" clearable style="width: 150px">
            <el-option
              v-for="value in Object.values(UserIdentityEnum).filter((v) => typeof v === 'number')"
              :key="value"
              :label="UserIdentityText[value]"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="timeRange" label="时间范围">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleTimeRangeChange"
          />
        </el-form-item>
        <el-form-item prop="auditStatus" label="审核状态">
          <el-select v-model="searchFormData.auditStatus" placeholder="审核状态" clearable style="width: 150px">
            <el-option
              v-for="value in Object.values(AuditStatusEnum).filter((v) => typeof v === 'number')"
              :key="value"
              :label="AuditStatusText[value]"
              :value="value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card v-loading="loading" shadow="never">
      <div class="toolbar-wrapper">
        <div>
          <!-- 可以添加批量操作按钮 -->
          <el-button type="danger" icon="Close" @click="batchHandle(1)">批量驳回</el-button>
          <el-button type="success" icon="Check" @click="batchHandle(2)">批量通过</el-button>
        </div>
        <div>
          <el-tooltip content="刷新" effect="light">
            <el-button type="primary" icon="RefreshRight" circle plain @click="getTableData" />
          </el-tooltip>
        </div>
      </div>

      <div class="table-wrapper">
        <el-table :data="tableData" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="60" :selectable="checkSelectable" />
          <el-table-column prop="id" label="用户ID" />
          <el-table-column prop="mobile" label="手机号" />
          <el-table-column prop="fullName" label="姓名" />
          <el-table-column prop="idCardNumber" label="身份证号码" />
          <el-table-column prop="userType" label="用户身份">
            <template #default="scope">
              {{
                scope.row.userType !== null && scope.row.userType !== undefined
                  ? UserIdentityText[scope.row.userType] || scope.row.userType
                  : "-"
              }}
            </template>
          </el-table-column>
          <el-table-column prop="auditStatus" label="审核状态" width="120">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.auditStatus)" effect="light">
                {{
                  scope.row.auditStatus !== null && scope.row.auditStatus !== undefined
                    ? AuditStatusText[scope.row.auditStatus] || scope.row.auditStatus
                    : "-"
                }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间">
            <template #default="scope">
              {{ formatDateTime(scope.row.auditApplyTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="300" fixed="right">
            <template #default="scope">
              <el-button
                v-if="scope.row.auditStatus === AuditStatusEnum.PENDING"
                type="success"
                text
                icon="Check"
                size="small"
                @click="handleApprove(scope.row)"
              >
                通过
              </el-button>
              <el-button
                v-if="scope.row.auditStatus === AuditStatusEnum.PENDING"
                type="danger"
                text
                icon="Close"
                size="small"
                @click="handleReject(scope.row)"
              >
                不通过
              </el-button>
              <el-button type="primary" text icon="View" size="small" @click="handleViewDetail(scope.row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pager-wrapper">
        <el-pagination
          background
          :layout="paginationData.layout"
          :page-sizes="paginationData.pageSizes"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
          :currentPage="paginationData.currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审核不通过原因对话框 -->
    <el-dialog v-model="rejectDialogVisible" title="审核不通过" width="500px" :before-close="handleRejectDialogClose">
      <el-form ref="rejectFormRef" :model="rejectFormData" :rules="rejectFormRules" label-width="100px">
        <el-form-item label="不通过原因" prop="reason">
          <el-input v-model="rejectFormData.reason" type="textarea" :rows="4" placeholder="请输入不通过原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeRejectDialog">取消</el-button>
          <el-button type="primary" @click="confirmReject">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 司机详情组件 -->
    <DriverDetail v-model="driverDetailVisible" :driver-data="currentDriverData" />

    <!-- 货主详情组件 -->
    <OwnerDetail v-model="ownerDetailVisible" :owner-data="currentOwnerData" />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue"
import { type FormInstance, type FormRules, ElMessage, ElMessageBox } from "element-plus"
import { usePagination } from "@/hooks/usePagination"
import {
  type QualificationDataModel,
  getQualificationListApi,
  auditUserApi,
  UserIdentityEnum,
  UserIdentityText,
  AuditStatusEnum,
  AuditStatusText
} from "@/api/transport/authManage/qualifications"
import { formatDateTime } from "@/utils/index"
import DriverDetail from "./components/DriverDetail.vue"
import OwnerDetail from "./components/OwnerDetail.vue"

defineOptions({
  name: "Qualifications"
})

// 分页
const { paginationData, changeCurrentPage, changePageSize } = usePagination()

// 加载状态
const loading = ref(false)

// 搜索表单
const searchFormRef = ref<FormInstance>()
const searchFormData = reactive({
  mobile: "",
  fullName: "",
  idCardNumber: "",
  userType: undefined as number | undefined,
  auditStatus: undefined as number | undefined,
  auditApplyBeginTime: "",
  auditApplyEndTime: ""
})

// 时间范围
const timeRange = ref<[string, string] | null>(null)

// 处理时间范围变化
const handleTimeRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchFormData.auditApplyBeginTime = value[0]
    searchFormData.auditApplyEndTime = value[1]
  } else {
    searchFormData.auditApplyBeginTime = ""
    searchFormData.auditApplyEndTime = ""
  }
}

// 表格数据
const tableData = ref<QualificationDataModel[]>([])
const selectedRows = ref<QualificationDataModel[]>([])

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
    const res = await getQualificationListApi({
      mobile: searchFormData.mobile || undefined,
      fullName: searchFormData.fullName || undefined,
      idCardNumber: searchFormData.idCardNumber || undefined,
      userType: searchFormData.userType !== undefined ? searchFormData.userType : undefined,
      auditStatus: searchFormData.auditStatus !== undefined ? searchFormData.auditStatus : undefined,
      auditApplyBeginTime: searchFormData.auditApplyBeginTime || undefined,
      auditApplyEndTime: searchFormData.auditApplyEndTime || undefined,
      page: paginationData.currentPage,
      limit: paginationData.pageSize
    })
    if (res.code === 700) {
      tableData.value = res.result
      paginationData.total = res.count
    }
  } catch {
    //
  }
  loading.value = false
}

// 搜索处理
const handleSearch = () => {
  paginationData.currentPage = 1
  getTableData()
}

const resetSearch = () => {
  searchFormRef.value?.resetFields()
  searchFormData.mobile = ""
  searchFormData.fullName = ""
  searchFormData.idCardNumber = ""
  searchFormData.userType = undefined
  searchFormData.auditStatus = undefined
  searchFormData.auditApplyBeginTime = ""
  searchFormData.auditApplyEndTime = ""
  timeRange.value = null
  getTableData()
}

// 分页处理
const handleSizeChange = (value: number) => {
  changePageSize(value)
  getTableData()
}

const handleCurrentChange = (value: number) => {
  changeCurrentPage(value)
  getTableData()
}

// 表格选择处理
const handleSelectionChange = (selection: QualificationDataModel[]) => {
  selectedRows.value = selection
}

// 检查行是否可选择（只有待审核状态才可选择）
const checkSelectable = (row: QualificationDataModel) => {
  return row.auditStatus === AuditStatusEnum.PENDING
}

// 获取状态标签类型
const getStatusTagType = (status: number | null | undefined) => {
  if (status === null || status === undefined) {
    return "info"
  }
  switch (status) {
    case AuditStatusEnum.PENDING:
      return "warning"
    case AuditStatusEnum.APPROVED:
      return "success"
    case AuditStatusEnum.REJECTED:
      return "danger"
    case AuditStatusEnum.NOT_AUDITED:
      return "info"
    default:
      return "info"
  }
}

// 审核通过
const handleApprove = (row: QualificationDataModel) => {
  ElMessageBox.confirm("确定要通过该用户的资质审核吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      auditUserApi({
        userIds: row.id,
        auditStatus: AuditStatusEnum.APPROVED
      }).then((res) => {
        if (res.code === 700) {
          ElMessage({ type: "success", message: "审核通过成功" })
          getTableData()
        }
      })
    })
    .catch(() => {})
}

// 审核不通过相关
const rejectDialogVisible = ref(false)
const rejectFormRef = ref<FormInstance>()
const rejectFormData = reactive({
  reason: ""
})
const rejectFormRules: FormRules = reactive({
  reason: [{ required: true, trigger: "blur", message: "请输入不通过原因" }]
})
let currentRejectRow: QualificationDataModel | null = null

const handleReject = (row: QualificationDataModel) => {
  currentRejectRow = row
  rejectDialogVisible.value = true
}

const handleRejectDialogClose = (done: () => void) => {
  rejectFormData.reason = ""
  currentRejectRow = null
  done()
}

const closeRejectDialog = () => {
  rejectDialogVisible.value = false
  rejectFormData.reason = ""
  currentRejectRow = null
}

const confirmReject = () => {
  if (!rejectFormRef.value || !currentRejectRow) return
  rejectFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await auditUserApi({
          userIds: currentRejectRow!.id,
          auditStatus: AuditStatusEnum.REJECTED,
          failReason: rejectFormData.reason
        })
        if (res.code === 700) {
          ElMessage({ type: "success", message: "审核不通过操作成功" })
          closeRejectDialog()
          getTableData()
        }
      } catch {
        //
      }
    }
  })
}

// 批量操作
const batchHandle = (type: number) => {
  if (selectedRows.value.length === 0) {
    ElMessage({ type: "warning", message: "请先选择要操作的记录" })
    return
  }
  if (type === 1) {
    // 批量驳回
    handleBatchReject()
  } else if (type === 2) {
    // 批量通过
    handleBatchApprove()
  }
}

const handleBatchReject = () => {
  ElMessageBox.prompt("请输入不通过原因", "批量审核不通过", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    inputPattern: /\S/,
    inputErrorMessage: "不通过原因不能为空"
  })
    .then(async ({ value }) => {
      try {
        const res = await auditUserApi({
          userIds: selectedRows.value.map((item) => item.id).join(","),
          auditStatus: AuditStatusEnum.REJECTED,
          failReason: value
        })
        if (res.code === 700) {
          ElMessage({ type: "success", message: "审核不通过操作成功" })
          getTableData()
        }
      } catch {
        //
      }
    })
    .catch(() => {})
}

const handleBatchApprove = () => {
  ElMessageBox.confirm("确定要通过所选用户的资质审核吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(async () => {
      try {
        const res = await auditUserApi({
          userIds: selectedRows.value.map((item) => item.id).join(","),
          auditStatus: AuditStatusEnum.APPROVED
        })
        if (res.code === 700) {
          ElMessage({ type: "success", message: "审核通过操作成功" })
          getTableData()
        }
      } catch {
        //
      }
    })
    .catch(() => {})
}

// 查看详情
const driverDetailVisible = ref(false)
const ownerDetailVisible = ref(false)
const currentDriverData = ref(null)
const currentOwnerData = ref(null)

const handleViewDetail = async (row: QualificationDataModel) => {
  try {
    const detailData = row

    // 根据用户身份显示不同的详情组件
    if (detailData.userType === UserIdentityEnum.DRIVER) {
      // 司机详情
      currentDriverData.value = {
        ...detailData
        // // 模拟司机相关数据
        // idCardFront: "https://via.placeholder.com/300x180?text=身份证正面",
        // idCardBack: "https://via.placeholder.com/300x180?text=身份证反面",
        // drivingLicenseMain: "https://via.placeholder.com/300x180?text=驾驶证主页",
        // drivingLicenseSub: "https://via.placeholder.com/300x180?text=驾驶证副页",
        // plateNumber: "京A12345",
        // vehicleOwner: detailData.fullName,
        // region: "北京市",
        // registrationDate: "2020-01-01",
        // vehicleLicenseMain: "https://via.placeholder.com/300x180?text=行驶证主页",
        // vehicleLicenseSub: "https://via.placeholder.com/300x180?text=行驶证副页",
        // transportPermit: "https://via.placeholder.com/300x180?text=道路运输许可证",
        // environmentalInfo: "https://via.placeholder.com/300x180?text=环保信息"
      }
      driverDetailVisible.value = true
    } else if (
      detailData.userType === UserIdentityEnum.OWNER_PERSONAL ||
      detailData.userType === UserIdentityEnum.OWNER_ENTERPRISE
    ) {
      // 货主详情
      currentOwnerData.value = {
        ...detailData
        // 模拟货主相关数据
        // idCardFront: "https://via.placeholder.com/300x180?text=身份证正面",
        // idCardBack: "https://via.placeholder.com/300x180?text=身份证反面",
        // legalPerson: "张三法人", // 故意设置与姓名不同，用于测试授权书警告
        // companyName: "奔驰奔驰奔驰有限公司",
        // businessLicenseNumber: "12356788876543",
        // businessLicenseImage: "https://via.placeholder.com/300x180?text=营业执照",
        // authorizationLetter: "https://via.placeholder.com/300x180?text=授权书"
      }
      ownerDetailVisible.value = true
    } else {
      // 配货站或其他身份，暂时使用货主详情
      currentOwnerData.value = {
        ...detailData
        // idCardFront: "https://via.placeholder.com/300x180?text=身份证正面",
        // idCardBack: "https://via.placeholder.com/300x180?text=身份证反面",
        // legalPerson: detailData.fullName, // 配货站姓名与法人一致，不显示警告
        // companyName: "配货站有限公司",
        // businessLicenseNumber: "配货站营业执照编号",
        // taxNumber: "91110000123456789X",
        // businessLicenseImage: "https://via.placeholder.com/300x180?text=营业执照",
        // authorizationLetter: "https://via.placeholder.com/300x180?text=授权书"
      }
      ownerDetailVisible.value = true
    }
  } catch {
    // 如果接口失败，使用当前行数据显示基本详情
    ElMessage({ type: "error", message: "获取详情失败" })
  }
}

// 初始化
getTableData()
</script>

<style lang="scss" scoped>
.table-wrapper {
  :deep(.el-table) {
    .el-table__empty-block {
      min-height: 200px;
    }
  }
}

.search-wrapper {
  margin-bottom: 8px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}
</style>
