<template>
  <el-dialog v-model="visible" title="订单详情" width="800px" :before-close="handleClose">
    <div v-loading="loading" class="order-detail">
      <!-- 货主信息 -->
      <div class="detail-section">
        <div class="section-title">货主信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">姓名：</span>
            <span class="value">{{ orderDetail?.shippersName || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">装货时间：</span>
            <span class="value"
              >{{ formatDate(orderDetail?.loadingDate) }} {{ orderDetail?.earliestLoadingTime }}-{{
                orderDetail?.latestLoadingTime
              }}</span
            >
          </div>
          <div class="info-item">
            <span class="label">手机号码：</span>
            <span class="value">{{ orderDetail?.mobile || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">货物名称：</span>
            <span class="value">{{ orderDetail?.cargoName || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户身份：</span>
            <span class="value">{{ orderDetail?.userType || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">包装方式：</span>
            <span class="value">{{ orderDetail?.packagingMethod || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">装货地址：</span>
            <span class="value">{{ formatFullAddress(orderDetail?.loadingAddress) }}</span>
          </div>
          <div class="info-item">
            <span class="label">总重量/体积：</span>
            <span class="value">{{ formatWeightVolume(orderDetail) || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">卸货地址：</span>
            <span class="value">{{ formatFullAddress(orderDetail?.dischargeAddress) }}</span>
          </div>
          <div class="info-item">
            <span class="label">车型：</span>
            <span class="value">{{ orderDetail?.vehicleType || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">运费：</span>
            <span class="value">{{ orderDetail?.bidAmount }}/{{ orderDetail?.bidType || "" }}</span>
          </div>
          <div class="info-item">
            <span class="label">车长：</span>
            <span class="value">{{ orderDetail?.vehicleLength || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金类型：</span>
            <span class="value">{{ orderDetail?.depositRefundType === "no_refund" ? "不退还" : "退还" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金：</span>
            <span class="value">{{ orderDetail?.depositAmount || 0 }}元</span>
          </div>
          <div class="info-item">
            <span class="label">温控范围：</span>
            <span class="value">{{
              temperatureList.find((item) => item.id == orderDetail?.temperatureControl)?.text || "-"
            }}</span>
          </div>
          <div class="info-item">
            <span class="label">备注：</span>
            <span class="value">{{ orderDetail?.remarks || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">需要车辆数：</span>
            <span class="value">{{ orderDetail?.requiredVehicles || "-" }}</span>
          </div>
        </div>
      </div>

      <!-- 司机信息 -->
      <div class="detail-section">
        <div class="section-title">司机信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">姓名：</span>
            <span class="value">{{ orderDetail?.driverName || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户身份：</span>
            <span class="value">{{ getUserType(orderDetail?.driverInfo?.userType) || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号码：</span>
            <span class="value">{{ orderDetail?.driverInfo?.mobile || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">车牌号：</span>
            <span class="value">{{ orderDetail?.driverInfo?.licensePlateNumber || "-" }}</span>
          </div>
        </div>

        <!-- 证件照片 -->
        <div class="photo-section">
          <div class="photo-item">
            <div class="photo-label">装货照片</div>
            <div class="photo-placeholder">
              <el-image
                v-if="orderDetail?.loadingImages"
                :src="orderDetail.loadingImages"
                style="width: 120px; height: 80px"
                fit="cover"
              />
              <div v-else class="empty-photo">暂无照片</div>
            </div>
          </div>
          <div class="photo-item">
            <div class="photo-label">卸货照片</div>
            <div class="photo-placeholder">
              <el-image
                v-if="orderDetail?.unloadingImages"
                :src="orderDetail.unloadingImages"
                style="width: 120px; height: 80px"
                fit="cover"
              />
              <div v-else class="empty-photo">暂无照片</div>
            </div>
          </div>
        </div>

        <div class="photo-section">
          <div class="photo-item">
            <div class="photo-label">回单照片</div>
            <div class="photo-placeholder">
              <el-image
                v-if="orderDetail?.deliveryProofImages"
                :src="orderDetail.deliveryProofImages"
                style="width: 120px; height: 80px"
                fit="cover"
              />
              <div v-else class="empty-photo">暂无照片</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订金信息 -->
      <div class="detail-section">
        <div class="section-title">订金信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">订金状态：</span>
            <span class="value">
              <el-tag
                v-if="orderDetail?.depositStatus !== null"
                :type="getDepositStatusTagType(orderDetail.depositStatus)"
                effect="plain"
              >
                {{ DepositStatusText[orderDetail.depositStatus] }}
              </el-tag>
              <span v-else>-</span>
            </span>
          </div>
          <div class="info-item">
            <span class="label">争议发起方：</span>
            <span class="value">{{ orderDetail?.arbitrationInitiator || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金类型：</span>
            <span class="value">{{ orderDetail?.depositRefundType === "no_refund" ? "不退还" : "退还" }}</span>
          </div>
          <div class="info-item">
            <span class="label">详情描述：</span>
            <span class="value">{{ orderDetail?.arbitrationDescription || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金：</span>
            <span class="value">{{ orderDetail?.depositAmount || 0 }}</span>
          </div>
        </div>

        <!-- 争议图片展示 -->
        <div class="dispute-photos">
          <div class="photo-label">争议图片：</div>
          <div class="photos" v-if="orderDetail?.photos && orderDetail.photos.length > 0">
            <el-image
              v-for="(photo, index) in orderDetail.photos"
              :key="index"
              :src="photo"
              style="width: 80px; height: 80px; margin-right: 8px; margin-bottom: 8px"
              fit="cover"
              :preview-src-list="orderDetail.photos"
              :initial-index="index"
            />
          </div>
          <div v-else class="no-photos">暂无争议图片</div>
        </div>

        <!-- 平台裁决 -->
        <div class="platform-decision">
          <div class="decision-label">*平台裁决：</div>
          <div class="decision-buttons">
            <el-button
              size="small"
              :type="decisionResult === 'to_owner' ? 'danger' : 'danger'"
              :plain="decisionResult !== 'to_owner'"
              @click="selectDecision('to_owner')"
            >
              订金支付给货主
            </el-button>
            <el-button
              size="small"
              :type="decisionResult === 'to_driver' ? 'warning' : 'warning'"
              :plain="decisionResult !== 'to_driver'"
              @click="selectDecision('to_driver')"
            >
              订金支付给司机
            </el-button>
          </div>
        </div>

        <!-- 裁决理由 -->
        <div class="reason-section">
          <div class="reason-label">*裁决理由：</div>
          <el-input
            v-model="decisionReason"
            type="textarea"
            :rows="3"
            placeholder="请输入裁决理由..."
            style="margin-top: 8px"
          />
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <el-button
            type="primary"
            :loading="submitting"
            :disabled="!decisionResult || !decisionReason.trim()"
            @click="submitDecision"
          >
            提交裁决结果
          </el-button>
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue"
import { type OrderDataModel, getOrderDetailApi, DepositStatusEnum, DepositStatusText } from "@/api/transport/order"
import { formatDate } from "@/utils"

interface Props {
  modelValue: boolean
  orderId?: number
}

interface Emits {
  (e: "update:modelValue", value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const orderDetail = ref<OrderDataModel>({} as OrderDataModel)
const decisionResult = ref<"to_owner" | "to_driver" | "">("")
const decisionReason = ref("")
const submitting = ref(false)

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.orderId) {
      fetchOrderDetail()
    }
  }
)

watch(visible, (newVal) => {
  emit("update:modelValue", newVal)
})

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!props.orderId) return

  loading.value = true
  try {
    const res = await getOrderDetailApi(props.orderId)
    if (res.code === 700) {
      orderDetail.value = res.result
    }
  } catch (error) {
    console.error("获取订单详情失败:", error)
  }
  loading.value = false
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  orderDetail.value = {} as OrderDataModel
  decisionResult.value = ""
  decisionReason.value = ""
  submitting.value = false
}

// 完整地址格式化函数
const formatFullAddress = (addressDetail: { province: string; city: string; county: string } | undefined) => {
  if (!addressDetail) return "-"

  const { province, city, county, address } = addressDetail
  return `${province}${city}${county}${address}`
}

// 重量/体积格式化函数
const formatWeightVolume = (item: OrderDataModel) => {
  if (!item) return "暂无信息"

  const infoArray = []

  // 2. 重量范围处理
  const minWeight = item.minWeight
  const maxWeight = item.maxWeight

  if (minWeight && maxWeight && minWeight !== maxWeight) {
    // 有起始和结束，显示区间
    infoArray.push(`${minWeight}-${maxWeight}吨`)
  } else if (minWeight && minWeight > 0) {
    // 只有起始值
    infoArray.push(`${minWeight}吨`)
  } else if (maxWeight && maxWeight > 0) {
    // 只有结束值
    infoArray.push(`${maxWeight}吨`)
  }
  // 都没有或都为0，不显示重量信息

  // 3. 体积范围处理
  const minVolume = item.minVolume
  const maxVolume = item.maxVolume

  if (minVolume && maxVolume && minVolume !== maxVolume) {
    // 有起始和结束，显示区间
    infoArray.push(`${minVolume}-${maxVolume}方`)
  } else if (minVolume && minVolume > 0) {
    // 只有起始值
    infoArray.push(`${minVolume}方`)
  } else if (maxVolume && maxVolume > 0) {
    // 只有结束值
    infoArray.push(`${maxVolume}方`)
  }
  // 都没有或都为0，不显示体积信息

  // 用空格连接所有信息，如果没有任何信息则返回默认文本
  return infoArray.length > 0 ? infoArray.join(" ") : "暂无信息"
}

const temperatureList: any = ref([
  {
    id: 1,
    text: "常温",
    degree: "环境温度",
    selected: true
  },
  {
    id: 2,
    text: "恒温",
    degree: "10-20°C",
    selected: false
  },
  {
    id: 3,
    text: "冷藏",
    degree: "0-10°C",
    selected: false
  },
  {
    id: 4,
    text: "冷冻",
    degree: "",
    selected: false
  }
])

// 获取用户类型
const getUserType = (userType: number) => {
  return userType == 1
    ? "个人货主"
    : userType == 2
      ? "司机"
      : userType == 3
        ? "配货站"
        : userType == 4
          ? "企业货主"
          : "--"
}

// 获取订金状态标签类型
const getDepositStatusTagType = (status: number) => {
  switch (status) {
    case DepositStatusEnum.PAID_PENDING_REFUND:
    case DepositStatusEnum.PAID_DRIVER_INITIATED_REFUND:
    case DepositStatusEnum.PAID_OWNER_INITIATED_DEDUCT:
      return "warning"
    case DepositStatusEnum.PAID_OWNER_AGREED_REFUND:
    case DepositStatusEnum.PAID_DRIVER_AGREED_DEDUCT:
    case DepositStatusEnum.PAID_TO_OWNER:
      return "success"
    case DepositStatusEnum.PAID_OWNER_REJECTED_REFUND:
    case DepositStatusEnum.PAID_DRIVER_REJECTED_DEDUCT:
      return "danger"
    case DepositStatusEnum.PAID_DEPOSIT_FROZEN:
    case DepositStatusEnum.ARBITRATION_COMPLETED:
      return "info"
    case DepositStatusEnum.REFUNDED_TO_DRIVER:
      return "primary"
    default:
      return "info"
  }
}

// 选择裁决结果
const selectDecision = (decision: "to_owner" | "to_driver") => {
  decisionResult.value = decision
}

// 提交裁决结果
const submitDecision = async () => {
  if (!decisionResult.value || !decisionReason.value.trim()) {
    return
  }

  submitting.value = true
  try {
    // TODO: 调用提交裁决结果的API
    console.log("提交裁决结果:", {
      orderId: props.orderId,
      decision: decisionResult.value,
      reason: decisionReason.value
    })

    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 提交成功后可以关闭弹窗或显示成功消息
    // ElMessage.success('裁决结果提交成功')
  } catch (error) {
    console.error("提交裁决结果失败:", error)
    // ElMessage.error('提交裁决结果失败')
  }
  submitting.value = false
}
</script>

<style lang="scss" scoped>
.order-detail {
  .detail-section {
    margin-bottom: 24px;

    .section-title {
      background: #409eff;
      color: white;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px 24px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          min-width: 80px;
          color: #666;
          font-size: 14px;
        }

        .value {
          flex: 1;
          color: #333;
          font-size: 14px;
        }
      }
    }

    .photo-section {
      display: flex;
      gap: 16px;
      margin-top: 16px;

      .photo-item {
        .photo-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .photo-placeholder {
          width: 120px;
          height: 80px;
          border: 1px dashed #ddd;
          display: flex;
          align-items: center;
          justify-content: center;

          .empty-photo {
            color: #999;
            font-size: 12px;
          }
        }
      }
    }

    .dispute-photos {
      margin-top: 16px;

      .photo-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .photos {
        display: flex;
        flex-wrap: wrap;
      }

      .no-photos {
        color: #999;
        font-size: 14px;
        padding: 20px;
        text-align: center;
        border: 1px dashed #ddd;
        background-color: #fafafa;
      }
    }

    .platform-decision {
      margin-top: 16px;

      .decision-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .decision-buttons {
        display: flex;
        gap: 8px;
      }
    }

    .reason-section {
      margin-top: 16px;

      .reason-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
    }

    .submit-section {
      margin-top: 16px;
      text-align: center;
    }
  }
}
</style>
