<template>
  <el-dialog v-model="visible" title="订单详情" width="800px" :before-close="handleClose">
    <div v-loading="loading" class="order-detail">
      <!-- 货主信息 -->
      <div class="detail-section">
        <div class="section-title">货主信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">姓名：</span>
            <span class="value">{{ orderDetail?.ownerName || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系时间：</span>
            <span class="value">{{ orderDetail?.contactTime || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号码：</span>
            <span class="value">{{ orderDetail?.mobile || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">货物名称：</span>
            <span class="value">{{ orderDetail?.cargoName || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户身份：</span>
            <span class="value">{{ orderDetail?.userType || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">包装方式：</span>
            <span class="value">{{ orderDetail?.packingType || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">装货地址：</span>
            <span class="value">{{ formatFullAddress(orderDetail?.loadingAddress) }}</span>
          </div>
          <div class="info-item">
            <span class="label">总重量：</span>
            <span class="value">{{ getWeightDisplay(orderDetail) || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">卸货地址：</span>
            <span class="value">{{ formatFullAddress(orderDetail?.dischargeAddress) }}</span>
          </div>
          <div class="info-item">
            <span class="label">总体积：</span>
            <span class="value">{{ getVolumeDisplay(orderDetail) || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">车型：</span>
            <span class="value">{{ orderDetail?.vehicleType || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">运费：</span>
            <span class="value">{{ orderDetail?.bidAmount }}{{ orderDetail?.bidType || "" }}</span>
          </div>
          <div class="info-item">
            <span class="label">车长：</span>
            <span class="value">{{ orderDetail?.vehicleLength || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金类型：</span>
            <span class="value">{{ orderDetail?.depositRefundType === "no_refund" ? "不退还" : "退还" }}</span>
          </div>
          <div class="info-item">
            <span class="label">占用车长：</span>
            <span class="value">{{ orderDetail?.occupiedLength || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金：</span>
            <span class="value">{{ orderDetail?.depositAmount || 0 }}</span>
          </div>
          <div class="info-item">
            <span class="label">运输范围：</span>
            <span class="value">{{ orderDetail?.transportScope || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">备注：</span>
            <span class="value">{{ orderDetail?.remark || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">需要车辆数：</span>
            <span class="value">{{ orderDetail?.vehicleCount || "-" }}</span>
          </div>
        </div>
      </div>

      <!-- 司机信息 -->
      <div class="detail-section">
        <div class="section-title">司机信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">姓名：</span>
            <span class="value">{{ orderDetail?.driverName || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">用户身份：</span>
            <span class="value">{{ orderDetail?.driverUserType || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机号码：</span>
            <span class="value">{{ orderDetail?.driverMobile || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">车牌号：</span>
            <span class="value">{{ orderDetail?.plateNumber || "-" }}</span>
          </div>
        </div>

        <!-- 证件照片 -->
        <div class="photo-section">
          <div class="photo-item">
            <div class="photo-label">装货照片</div>
            <div class="photo-placeholder">
              <el-image
                v-if="orderDetail?.loadingPhoto"
                :src="orderDetail.loadingPhoto"
                style="width: 120px; height: 80px"
                fit="cover"
              />
              <div v-else class="empty-photo">暂无照片</div>
            </div>
          </div>
          <div class="photo-item">
            <div class="photo-label">卸货照片</div>
            <div class="photo-placeholder">
              <el-image
                v-if="orderDetail?.unloadingPhoto"
                :src="orderDetail.unloadingPhoto"
                style="width: 120px; height: 80px"
                fit="cover"
              />
              <div v-else class="empty-photo">暂无照片</div>
            </div>
          </div>
        </div>

        <div class="photo-section">
          <div class="photo-item">
            <div class="photo-label">回单照片</div>
            <div class="photo-placeholder">
              <el-image
                v-if="orderDetail?.receiptPhoto"
                :src="orderDetail.receiptPhoto"
                style="width: 120px; height: 80px"
                fit="cover"
              />
              <div v-else class="empty-photo">暂无照片</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 订金信息 -->
      <div class="detail-section">
        <div class="section-title">订金信息</div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">订金状态：</span>
            <span class="value">
              <el-tag
                v-if="orderDetail?.depositStatus !== null"
                :type="getDepositStatusTagType(orderDetail.depositStatus)"
                effect="plain"
              >
                {{ DepositStatusText[orderDetail.depositStatus] }}
              </el-tag>
              <span v-else>-</span>
            </span>
          </div>
          <div class="info-item">
            <span class="label">参议发起方：</span>
            <span class="value">{{ orderDetail?.arbitrationInitiator || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金类型：</span>
            <span class="value">{{ orderDetail?.depositRefundType === "no_refund" ? "不退还" : "退还" }}</span>
          </div>
          <div class="info-item">
            <span class="label">详情描述：</span>
            <span class="value">{{ orderDetail?.arbitrationDescription || "-" }}</span>
          </div>
          <div class="info-item">
            <span class="label">订金：</span>
            <span class="value">{{ orderDetail?.depositAmount || 0 }}</span>
          </div>
        </div>

        <!-- 照片展示 -->
        <div class="photo-gallery" v-if="orderDetail?.photos && orderDetail.photos.length > 0">
          <div class="photo-label">照片：</div>
          <div class="photos">
            <el-image
              v-for="(photo, index) in orderDetail.photos"
              :key="index"
              :src="photo"
              style="width: 60px; height: 60px; margin-right: 8px"
              fit="cover"
              :preview-src-list="orderDetail.photos"
              :initial-index="index"
            />
          </div>
        </div>

        <!-- 平台备注 -->
        <div class="platform-note">
          <div class="note-label">*平台备注：</div>
          <div class="note-buttons">
            <el-button size="small" type="danger" plain>订金支付给货主</el-button>
            <el-button size="small" type="warning" plain>订金支付给司机</el-button>
          </div>
        </div>

        <!-- 备注理由 -->
        <div class="reason-section">
          <div class="reason-label">*备注理由：</div>
          <el-input
            v-model="remarkReason"
            type="textarea"
            :rows="3"
            placeholder="请输入备注理由..."
            style="margin-top: 8px"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue"
import { type OrderDataModel, getOrderDetailApi, DepositStatusEnum, DepositStatusText } from "@/api/transport/order"

interface Props {
  modelValue: boolean
  orderId?: number
}

interface Emits {
  (e: "update:modelValue", value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const visible = ref(false)
const loading = ref(false)
const orderDetail = ref<OrderDataModel | null>(null)
const remarkReason = ref("")

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
    if (newVal && props.orderId) {
      fetchOrderDetail()
    }
  }
)

watch(visible, (newVal) => {
  emit("update:modelValue", newVal)
})

// 获取订单详情
const fetchOrderDetail = async () => {
  if (!props.orderId) return

  loading.value = true
  try {
    const res = await getOrderDetailApi(props.orderId)
    if (res.code === 700) {
      orderDetail.value = res.result
    }
  } catch (error) {
    console.error("获取订单详情失败:", error)
  }
  loading.value = false
}

// 关闭弹窗
const handleClose = () => {
  visible.value = false
  orderDetail.value = null
  remarkReason.value = ""
}

// 完整地址格式化函数
const formatFullAddress = (address: { province: string; city: string; district: string } | undefined) => {
  if (!address) return "-"

  const { province, city, district } = address
  return `${province}${city}${district}`
}

// 重量显示函数
const getWeightDisplay = (row: OrderDataModel | null) => {
  if (!row) return ""
  const { weightMin, weightMax } = row
  if (weightMin === null && weightMax === null) return ""

  if (weightMin !== null && weightMax !== null) {
    if (weightMin === weightMax) {
      return `${weightMin}吨`
    }
    return `${weightMin}～${weightMax}吨`
  }

  if (weightMin !== null) return `${weightMin}吨`
  if (weightMax !== null) return `${weightMax}吨`

  return ""
}

// 体积显示函数
const getVolumeDisplay = (row: OrderDataModel | null) => {
  if (!row) return ""
  const { volumeMin, volumeMax } = row
  if (volumeMin === null && volumeMax === null) return ""

  if (volumeMin !== null && volumeMax !== null) {
    if (volumeMin === volumeMax) {
      return `${volumeMin}方`
    }
    return `${volumeMin}～${volumeMax}方`
  }

  if (volumeMin !== null) return `${volumeMin}方`
  if (volumeMax !== null) return `${volumeMax}方`

  return ""
}

// 获取订金状态标签类型
const getDepositStatusTagType = (status: number) => {
  switch (status) {
    case DepositStatusEnum.PAID_PENDING_REFUND:
    case DepositStatusEnum.PAID_DRIVER_INITIATED_REFUND:
    case DepositStatusEnum.PAID_OWNER_INITIATED_DEDUCT:
      return "warning"
    case DepositStatusEnum.PAID_OWNER_AGREED_REFUND:
    case DepositStatusEnum.PAID_DRIVER_AGREED_DEDUCT:
    case DepositStatusEnum.PAID_TO_OWNER:
      return "success"
    case DepositStatusEnum.PAID_OWNER_REJECTED_REFUND:
    case DepositStatusEnum.PAID_DRIVER_REJECTED_DEDUCT:
      return "danger"
    case DepositStatusEnum.PAID_DEPOSIT_FROZEN:
    case DepositStatusEnum.ARBITRATION_COMPLETED:
      return "info"
    case DepositStatusEnum.REFUNDED_TO_DRIVER:
      return "primary"
    default:
      return "info"
  }
}
</script>

<style lang="scss" scoped>
.order-detail {
  .detail-section {
    margin-bottom: 24px;

    .section-title {
      background: #409eff;
      color: white;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: bold;
      margin-bottom: 16px;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px 24px;

      .info-item {
        display: flex;
        align-items: center;

        .label {
          min-width: 80px;
          color: #666;
          font-size: 14px;
        }

        .value {
          flex: 1;
          color: #333;
          font-size: 14px;
        }
      }
    }

    .photo-section {
      display: flex;
      gap: 16px;
      margin-top: 16px;

      .photo-item {
        .photo-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .photo-placeholder {
          width: 120px;
          height: 80px;
          border: 1px dashed #ddd;
          display: flex;
          align-items: center;
          justify-content: center;

          .empty-photo {
            color: #999;
            font-size: 12px;
          }
        }
      }
    }

    .photo-gallery {
      margin-top: 16px;

      .photo-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .photos {
        display: flex;
        flex-wrap: wrap;
      }
    }

    .platform-note {
      margin-top: 16px;

      .note-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }

      .note-buttons {
        display: flex;
        gap: 8px;
      }
    }

    .reason-section {
      margin-top: 16px;

      .reason-label {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
