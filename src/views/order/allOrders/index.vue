<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card v-loading="loading" shadow="never" class="search-wrapper">
      <el-form ref="searchFormRef" :inline="true" :model="searchFormData">
        <el-form-item prop="id" label="订单编号">
          <el-input v-model="searchFormData.id" placeholder="订单编号" clearable />
        </el-form-item>
        <el-form-item prop="mobile" label="手机号">
          <el-input v-model="searchFormData.mobile" placeholder="手机号" clearable />
        </el-form-item>
        <el-form-item prop="loadingAddress" label="装货地址">
          <el-input v-model="searchFormData.loadingAddress" placeholder="装货地址" clearable />
        </el-form-item>
        <el-form-item prop="unloadingAddress" label="卸货地址">
          <el-input v-model="searchFormData.unloadingAddress" placeholder="卸货地址" clearable />
        </el-form-item>
        <el-form-item prop="cargoName" label="货物名称">
          <el-input v-model="searchFormData.cargoName" placeholder="货物名称" clearable />
        </el-form-item>
        <el-form-item prop="depositStatus" label="订金状态">
          <el-select v-model="searchFormData.depositStatus" placeholder="请选择订金状态" clearable style="width: 150px">
            <el-option v-for="(text, value) in DepositStatusText" :key="value" :label="text" :value="Number(value)" />
          </el-select>
        </el-form-item>
        <el-form-item prop="orderStatus" label="订单状态">
          <el-select v-model="searchFormData.orderStatus" placeholder="请选择订单状态" clearable style="width: 150px">
            <el-option v-for="(text, value) in OrderStatusText" :key="value" :label="text" :value="Number(value)" />
          </el-select>
        </el-form-item>
        <el-form-item prop="timeRange" label="时间范围">
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            @change="handleTimeRangeChange"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">查询</el-button>
          <el-button icon="Refresh" @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格区域 -->
    <el-card v-loading="loading" shadow="never">
      <div class="toolbar-wrapper">
        <div>
          <!-- 可以添加批量操作按钮 -->
        </div>
        <div>
          <el-tooltip content="刷新" effect="light">
            <el-button type="primary" icon="RefreshRight" circle plain @click="getTableData" />
          </el-tooltip>
        </div>
      </div>
      <div class="table-wrapper">
        <el-table :data="tableData" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column prop="orderNo" label="订单号" min-width="100" />
          <el-table-column prop="loadingAddress" label="装货地" min-width="120" show-overflow-tooltip />
          <el-table-column prop="unloadingAddress" label="卸货地" min-width="120" show-overflow-tooltip />
          <el-table-column prop="cargoName" label="货物名称" min-width="100" show-overflow-tooltip />
          <el-table-column prop="weight" label="重量/体积" min-width="100" align="center" />
          <el-table-column prop="distance" label="运费" min-width="80" align="center" />
          <el-table-column prop="depositAmount" label="订金金额" min-width="100" align="center" />
          <el-table-column prop="deposit" label="订金" min-width="80" align="center" />
          <el-table-column prop="depositStatus" label="订金状态" min-width="120" align="center">
            <template #default="scope">
              <el-tag
                v-if="scope.row.depositStatus !== null"
                :type="getDepositStatusTagType(scope.row.depositStatus)"
                effect="plain"
              >
                {{ DepositStatusText[scope.row.depositStatus] }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="orderStatus" label="订单状态" min-width="120" align="center">
            <template #default="scope">
              <el-tag
                v-if="scope.row.orderStatus !== null"
                :type="getOrderStatusTagType(scope.row.orderStatus)"
                effect="plain"
              >
                {{ OrderStatusText[scope.row.orderStatus] }}
              </el-tag>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" min-width="160" align="center">
            <template #default="scope">
              {{ formatDateTime(scope.row.createdAt) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" width="80" align="center">
            <template #default="scope">
              <el-button type="primary" text icon="View" size="small" @click="viewDetail(scope.row)"> 详情 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pager-wrapper">
        <el-pagination
          background
          :layout="paginationData.layout"
          :page-sizes="paginationData.pageSizes"
          :total="paginationData.total"
          :page-size="paginationData.pageSize"
          :currentPage="paginationData.currentPage"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue"
import { type FormInstance, ElMessage } from "element-plus"
import { usePagination } from "@/hooks/usePagination"
import {
  type OrderDataModel,
  getOrderListApi,
  OrderStatusEnum,
  OrderStatusText,
  DepositStatusEnum,
  DepositStatusText
} from "@/api/transport/order"
import { formatDateTime } from "@/utils/index"

defineOptions({
  name: "AllOrders"
})

// 分页
const { paginationData, changeCurrentPage, changePageSize } = usePagination()

// 加载状态
const loading = ref(false)

// 搜索表单
const searchFormRef = ref<FormInstance>()
const searchFormData = reactive({
  id: "",
  mobile: "",
  loadingAddress: "",
  unloadingAddress: "",
  cargoName: "",
  depositStatus: undefined as number | undefined,
  orderStatus: undefined as number | undefined,
  startTime: "",
  endTime: ""
})

// 时间范围
const timeRange = ref<[string, string] | null>(null)

// 处理时间范围变化
const handleTimeRangeChange = (value: [string, string] | null) => {
  if (value) {
    searchFormData.startTime = value[0]
    searchFormData.endTime = value[1]
  } else {
    searchFormData.startTime = ""
    searchFormData.endTime = ""
  }
}

// 表格数据
const tableData = ref<OrderDataModel[]>([])
const selectedRows = ref<OrderDataModel[]>([])

// 获取表格数据
const getTableData = async () => {
  loading.value = true
  try {
    const res = await getOrderListApi({
      id: searchFormData.id || undefined,
      mobile: searchFormData.mobile || undefined,
      loadingAddressName: searchFormData.loadingAddressName || undefined,
      unloadingAddressName: searchFormData.unloadingAddressName || undefined,
      cargoName: searchFormData.cargoName || undefined,
      depositStatus: searchFormData.depositStatus !== undefined ? searchFormData.depositStatus : undefined,
      orderStatus: searchFormData.orderStatus !== undefined ? searchFormData.orderStatus : undefined,
      startTime: searchFormData.startTime || undefined,
      endTime: searchFormData.endTime || undefined,
      page: paginationData.currentPage,
      limit: paginationData.pageSize
    })
    if (res.code === 700) {
      tableData.value = res.result
      paginationData.total = res.count
    }
  } catch {
    //
  }
  loading.value = false
}

// 搜索处理
const handleSearch = () => {
  paginationData.currentPage = 1
  getTableData()
}

const resetSearch = () => {
  searchFormRef.value?.resetFields()
  searchFormData.id = ""
  searchFormData.mobile = ""
  searchFormData.loadingAddressName = ""
  searchFormData.unloadingAddressName = ""
  searchFormData.cargoName = ""
  searchFormData.depositStatus = undefined
  searchFormData.orderStatus = undefined
  searchFormData.startTime = ""
  searchFormData.endTime = ""
  timeRange.value = null
  getTableData()
}

// 分页处理
const handleSizeChange = (value: number) => {
  changePageSize(value)
  getTableData()
}

const handleCurrentChange = (value: number) => {
  changeCurrentPage(value)
  getTableData()
}

// 表格选择处理
const handleSelectionChange = (selection: OrderDataModel[]) => {
  selectedRows.value = selection
}

// 获取订金状态标签类型
const getDepositStatusTagType = (status: number) => {
  switch (status) {
    case DepositStatusEnum.PAID_PENDING_REFUND:
    case DepositStatusEnum.PAID_DRIVER_INITIATED_REFUND:
    case DepositStatusEnum.PAID_OWNER_INITIATED_DEDUCT:
      return "warning"
    case DepositStatusEnum.PAID_OWNER_AGREED_REFUND:
    case DepositStatusEnum.PAID_DRIVER_AGREED_DEDUCT:
    case DepositStatusEnum.PAID_TO_OWNER:
      return "success"
    case DepositStatusEnum.PAID_OWNER_REJECTED_REFUND:
    case DepositStatusEnum.PAID_DRIVER_REJECTED_DEDUCT:
      return "danger"
    case DepositStatusEnum.PAID_DEPOSIT_FROZEN:
    case DepositStatusEnum.ARBITRATION_COMPLETED:
      return "info"
    case DepositStatusEnum.REFUNDED_TO_DRIVER:
      return "primary"
    default:
      return ""
  }
}

// 获取订单状态标签类型
const getOrderStatusTagType = (status: number) => {
  switch (status) {
    case OrderStatusEnum.PENDING_ORDER:
      return "warning"
    case OrderStatusEnum.ACCEPTED:
    case OrderStatusEnum.PRICE_CHANGE_CONFIRMED:
      return "primary"
    case OrderStatusEnum.PRICE_CHANGE_PENDING:
      return "warning"
    case OrderStatusEnum.PRICE_CHANGE_REJECTED:
      return "danger"
    case OrderStatusEnum.ARRIVED_LOADING:
    case OrderStatusEnum.LOADING:
    case OrderStatusEnum.IN_TRANSIT:
    case OrderStatusEnum.ARRIVED_UNLOADING:
      return "info"
    case OrderStatusEnum.COMPLETED_PENDING:
      return "warning"
    case OrderStatusEnum.COMPLETED_CONFIRMED:
      return "success"
    case OrderStatusEnum.CANCELLED:
      return "danger"
    case OrderStatusEnum.COMPLETED_ABNORMAL:
      return "info"
    default:
      return ""
  }
}

// 查看详情
const viewDetail = (row: OrderDataModel) => {
  ElMessage.info(`查看订单 ${row.id} 的详情`)
  // TODO: 实现详情查看功能
}

// 初始化
getTableData()
</script>

<style lang="scss" scoped>
.table-wrapper {
  :deep(.el-table) {
    .el-table__empty-block {
      min-height: 200px;
    }
  }
}

.search-wrapper {
  margin-bottom: 8px;

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}
</style>
