import router from "@/router"
import { useUserStoreHook } from "@/store/modules/user"
import { usePermissionStoreHook } from "@/store/modules/permission"
// import { ElMessage } from "element-plus"
import { setRouteChange } from "@/hooks/useRouteListener"
import { useTitle } from "@/hooks/useTitle"
import isWhiteList from "@/config/white-list"
import NProgress from "nprogress"
import "nprogress/nprogress.css"
import routeSettings from "@/config/route"
import { flatMultiLevelRoutes } from "@/router/helper"

const { setTitle } = useTitle()
NProgress.configure({ showSpinner: false })

router.beforeEach(async (to, _from, next) => {
  NProgress.start()
  const userStore = useUserStoreHook()
  const permissionStore = usePermissionStoreHook()
  // 判断该用户是否登录
  if (userStore.token) {
    if (to.path === "/login") {
      // 如果已经登录，并准备进入 Login 页面，则重定向到主页
      next({ path: "/" })
      NProgress.done()
    } else {
      // 检查当前用户是否已经获取到用户信息
      if (userStore.userInfo.username === "") {
        try {
          // 获取用户信息
          await userStore.getInfo()
          // 获取路由
          await permissionStore.setRoutes()
          // 将'有访问权限的动态路由' 添加到 Router 中
          // 是否处理为两层布局嵌套
          if (routeSettings.thirdLevelRouteCache) {
            const flatMultiLevelArr = flatMultiLevelRoutes(permissionStore.dynamicRoutes)
            flatMultiLevelArr.forEach((route) => {
              router.addRoute(route)
            })
          } else {
            permissionStore.dynamicRoutes.forEach((route) => {
              router.addRoute(route)
            })
          }
          // 确保添加路由已完成
          // 设置 replace: true, 因此导航将不会留下历史记录
          next({ ...to, replace: true })
        } catch (err: any) {
          // 过程中发生任何错误，都直接重置 Token，并重定向到登录页面
          userStore.resetToken()
          console.log(err)
          // ElMessage.error(err.message || "路由守卫过程发生错误")
          next("/login")
          NProgress.done()
        }
      } else {
        next()
      }
    }
  } else {
    // 如果没有 Token
    if (isWhiteList(to)) {
      // 如果在免登录的白名单中，则直接进入
      next()
    } else {
      // 其他没有访问权限的页面将被重定向到登录页面
      next("/login")
      NProgress.done()
    }
  }
})

router.afterEach((to) => {
  setRouteChange(to)
  setTitle(to.meta.title)
  NProgress.done()
})
